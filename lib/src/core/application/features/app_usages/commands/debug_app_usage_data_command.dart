import 'package:mediatr/mediatr.dart';
import 'package:whph/src/core/application/features/app_usages/services/abstraction/i_app_usage_service.dart';
import 'package:whph/src/infrastructure/android/features/app_usage/android_app_usage_service.dart';
import 'package:whph/src/core/shared/utils/logger.dart';

class DebugAppUsageDataCommand implements IRequest<DebugAppUsageDataCommandResponse> {
  DebugAppUsageDataCommand();
}

class DebugAppUsageDataCommandResponse {
  final bool success;
  final String message;

  DebugAppUsageDataCommandResponse({
    required this.success,
    required this.message,
  });
}

class DebugAppUsageDataCommandHandler implements IRequestHandler<DebugAppUsageDataCommand, DebugAppUsageDataCommandResponse> {
  final IAppUsageService _appUsageService;

  DebugAppUsageDataCommandHandler(this._appUsageService);

  @override
  Future<DebugAppUsageDataCommandResponse> call(DebugAppUsageDataCommand request) async {
    try {
      Logger.info('Starting app usage data debugging...');
      
      // Check if the service is AndroidAppUsageService
      if (_appUsageService is AndroidAppUsageService) {
        final androidService = _appUsageService as AndroidAppUsageService;
        
        // Run the debug method
        await androidService.debugAppUsagePackageBehavior();
        
        // Also trigger a manual collection to see current behavior
        Logger.info('Triggering manual data collection for debugging...');
        await androidService.startTracking();
        
        return DebugAppUsageDataCommandResponse(
          success: true,
          message: 'Debug data collection completed. Check logs for detailed information.',
        );
      } else {
        return DebugAppUsageDataCommandResponse(
          success: false,
          message: 'Debug method only available on Android platform.',
        );
      }
    } catch (e) {
      Logger.error('Error in debug app usage data command: $e');
      return DebugAppUsageDataCommandResponse(
        success: false,
        message: 'Debug failed: $e',
      );
    }
  }
}
