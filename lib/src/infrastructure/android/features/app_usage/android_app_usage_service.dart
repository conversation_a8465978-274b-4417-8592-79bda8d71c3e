import 'dart:async';
import 'package:app_usage/app_usage.dart' as app_usage_package;
import 'package:flutter/services.dart';
import 'package:whph/src/core/application/features/app_usages/services/abstraction/base_app_usage_service.dart';
import 'package:whph/src/infrastructure/android/constants/android_app_constants.dart';
import 'package:whph/src/core/shared/utils/logger.dart';
import 'package:whph/src/core/application/features/settings/services/abstraction/i_setting_repository.dart';
import 'package:whph/src/core/domain/features/settings/setting.dart';
import 'package:whph/src/core/application/shared/utils/key_helper.dart' as app_key_helper;
import 'package:whph/src/presentation/ui/shared/constants/setting_keys.dart';

class AndroidAppUsageService extends BaseAppUsageService {
  static final appUsageStatsChannel = MethodChannel(AndroidAppConstants.channels.appUsageStats);
  static final workManagerChannel = MethodChannel(AndroidAppConstants.channels.workManager);
  final app_usage_package.AppUsage _appUsage = app_usage_package.AppUsage();
  final ISettingRepository _settingRepository;

  AndroidAppUsageService(
    super.appUsageRepository,
    super.appUsageTimeRecordRepository,
    super.appUsageTagRuleRepository,
    super.appUsageTagRepository,
    super.appUsageIgnoreRuleRepository,
    this._settingRepository,
  );

  @override
  Future<void> startTracking() async {
    final hasPermission = await checkUsageStatsPermission();
    if (!hasPermission) {
      Logger.warning('Usage stats permission not granted. Cannot start tracking.');
      return;
    }

    // Initial fetch for the current partial hour to capture immediate usage.
    await _fetchAndSaveCurrentHourUsage();

    // Start WorkManager periodic work for background collection
    await _startWorkManagerTracking();

    // Set up method channel listener for WorkManager triggers
    _setupWorkManagerListener();
  }

  /// Starts WorkManager periodic work for background app usage collection
  Future<void> _startWorkManagerTracking() async {
    try {
      await workManagerChannel.invokeMethod('startPeriodicAppUsageWork');
      // Default interval: 60 minutes (1 hour)
      Logger.info('WorkManager periodic app usage tracking started');
    } catch (e) {
      Logger.error('Failed to start WorkManager tracking: $e');
    }
  }

  /// Sets up method channel listener for WorkManager triggers
  void _setupWorkManagerListener() {
    appUsageStatsChannel.setMethodCallHandler((call) async {
      if (call.method == 'triggerCollection') {
        Logger.info('WorkManager triggered app usage collection');
        await _fetchAndSaveCurrentHourUsage();
      }
    });
  }

  /// Fetches app usage for individual hours using incremental collection to avoid data duplication.
  /// Uses improved time zone handling and incremental collection approach for accurate statistics.
  Future<void> _fetchAndSaveCurrentHourUsage() async {
    // Permission should have been checked by the caller (startTracking or timer callback).
    try {
      // Use local time for collection but ensure consistent time zone handling
      DateTime now = DateTime.now();
      DateTime? lastCollection = await _getLastCollectionTimestamp();

      // If this is the first time running, start from the beginning of current hour
      if (lastCollection == null) {
        DateTime currentHourStart = DateTime(now.year, now.month, now.day, now.hour, 0, 0, 0, 0);
        await _collectUsageForSingleHour(currentHourStart);
        await _saveLastCollectionTimestamp(now);
        return;
      }

      // Calculate which hours need to be processed since last collection
      List<DateTime> hoursToProcess = _getHoursToProcess(lastCollection, now);

      if (hoursToProcess.isEmpty) {
        Logger.info('No new hours to process since last collection: $lastCollection');
        return;
      }

      Logger.info('Processing ${hoursToProcess.length} hours since last collection');

      // Log all hours to be processed to check for duplicates
      for (int i = 0; i < hoursToProcess.length; i++) {
        Logger.info('Hour ${i + 1}/${hoursToProcess.length}: ${hoursToProcess[i]}');
      }

      // Process each hour individually using incremental method
      for (DateTime hourStart in hoursToProcess) {
        Logger.info('=== PROCESSING HOUR ${hoursToProcess.indexOf(hourStart) + 1}/${hoursToProcess.length}: $hourStart ===');
        await _collectUsageForSingleHour(hourStart);

        // Add a small delay between collections to avoid overwhelming the system
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Update the last collection timestamp
      await _saveLastCollectionTimestamp(now);
      Logger.info('Successfully processed ${hoursToProcess.length} hours');
    } catch (e) {
      Logger.error('Error in _fetchAndSaveCurrentHourUsage: $e');
    }
  }

  /// Determines which hours need to be processed since the last collection.
  /// Returns a list of hour start times that need data collection.
  List<DateTime> _getHoursToProcess(DateTime lastCollection, DateTime now) {
    List<DateTime> hoursToProcess = [];

    // Start from the hour after the last collection
    DateTime startHour = DateTime(
      lastCollection.year,
      lastCollection.month,
      lastCollection.day,
      lastCollection.hour,
      0, 0, 0, 0
    );

    // If we're in the same hour as last collection, move to next hour
    if (startHour.isAtSameMomentAs(DateTime(
      lastCollection.year, lastCollection.month, lastCollection.day, lastCollection.hour, 0, 0, 0, 0))) {
      startHour = startHour.add(const Duration(hours: 1));
    }

    DateTime currentHour = DateTime(now.year, now.month, now.day, now.hour, 0, 0, 0, 0);

    // Add all complete hours between last collection and now
    DateTime processingHour = startHour;
    while (processingHour.isBefore(currentHour) || processingHour.isAtSameMomentAs(currentHour)) {
      hoursToProcess.add(processingHour);
      processingHour = processingHour.add(const Duration(hours: 1));
    }

    return hoursToProcess;
  }

  /// Collects usage data using incremental approach to avoid cumulative data issues.
  /// This method fetches usage for a larger time period and calculates incremental differences.
  Future<void> _collectUsageForSingleHour(DateTime hourStart) async {
    try {
      Logger.info('=== COLLECTING HOUR: $hourStart ===');

      // Get usage data using incremental approach to avoid cumulative data issues
      Map<String, int> hourlyUsage = await _getIncrementalUsageForHour(hourStart);

      if (hourlyUsage.isEmpty) {
        Logger.info('No usage data to save for hour $hourStart');
        return;
      }

      int totalSecondsToSave = 0;
      int recordsSaved = 0;

      // Save each app's incremental usage for this hour
      for (MapEntry<String, int> entry in hourlyUsage.entries) {
        String appName = entry.key;
        int incrementalSeconds = entry.value;

        if (incrementalSeconds <= 0) {
          if (incrementalSeconds < 0) {
            Logger.warning('Negative incremental usage for $appName (${incrementalSeconds}s). Skipping.');
          }
          continue;
        }

        totalSecondsToSave += incrementalSeconds;

        // Log Chrome specifically
        if (appName.toLowerCase().contains('chrome')) {
          Logger.warning('SAVING CHROME: $appName = ${incrementalSeconds}s (${(incrementalSeconds / 60).toStringAsFixed(1)}min) for hour $hourStart');
        }

        // Save the incremental usage for this hour
        await saveTimeRecord(
          appName,
          incrementalSeconds,
          overwrite: true, // Overwrite any existing data for this hour
          customDateTime: hourStart,
        );

        recordsSaved++;
      }

      Logger.info('=== HOUR COLLECTION COMPLETE ===');
      Logger.info('Saved $recordsSaved records, total: ${totalSecondsToSave}s (${(totalSecondsToSave / 60).toStringAsFixed(1)}min) for hour $hourStart');

      // Validate what we just saved
      await _validateSavedData(hourStart);

    } catch (e) {
      Logger.error('Error collecting incremental usage for hour $hourStart: $e');
    }
  }

  /// Calculates incremental usage for a specific hour by comparing with previous data.
  /// This addresses the issue where app_usage package returns cumulative data.
  Future<Map<String, int>> _getIncrementalUsageForHour(DateTime hourStart) async {
    try {
      DateTime hourEnd = hourStart.add(const Duration(hours: 1));
      DateTime previousHourStart = hourStart.subtract(const Duration(hours: 1));

      // Check if this is the first hour we're collecting (no previous data available)
      DateTime? lastCollection = await _getLastCollectionTimestamp();
      bool isFirstCollection = lastCollection == null;

      Logger.info('=== INCREMENTAL CALCULATION DEBUG ===');
      Logger.info('Target hour: $hourStart to $hourEnd');
      Logger.info('Is first collection: $isFirstCollection');
      Logger.info('Last collection timestamp: $lastCollection');

      // For the very first collection, use a different approach
      if (isFirstCollection) {
        Logger.info('Using direct collection for first hour');

        // Run diagnostic to understand package behavior
        await diagnoseAppUsagePackageBehavior(hourStart);

        Map<String, int> directResult = await _getDirectUsageForHour(hourStart);
        Logger.info('Direct collection returned ${directResult.length} apps');

        // Log Chrome specifically for debugging
        for (String appName in directResult.keys) {
          if (appName.toLowerCase().contains('chrome')) {
            int seconds = directResult[appName]!;
            Logger.warning('CHROME DIRECT: $appName = ${seconds}s (${(seconds / 60).toStringAsFixed(1)}min) for first hour $hourStart');
          }
        }

        return directResult;
      }

      // Check if we have enough historical data for incremental calculation
      DateTime earliestDataTime = lastCollection.subtract(const Duration(hours: 2));
      if (hourStart.isBefore(earliestDataTime)) {
        Logger.info('Hour too far back, using direct collection');
        return await _getDirectUsageForHour(hourStart);
      }

      Logger.info('Using incremental calculation');
      Logger.info('Previous hour: $previousHourStart to $hourStart');
      Logger.info('Current period: $previousHourStart to $hourEnd');

      // Get cumulative usage up to the end of target hour
      List<app_usage_package.AppUsageInfo> currentPeriodStats =
          await _appUsage.getAppUsage(previousHourStart, hourEnd);

      // Get cumulative usage up to the start of target hour (previous hour)
      List<app_usage_package.AppUsageInfo> previousPeriodStats =
          await _appUsage.getAppUsage(previousHourStart, hourStart);

      Logger.info('Current period returned ${currentPeriodStats.length} apps');
      Logger.info('Previous period returned ${previousPeriodStats.length} apps');

      // Create maps for easier comparison
      Map<String, int> currentUsage = {};
      Map<String, int> previousUsage = {};

      for (var app in currentPeriodStats) {
        currentUsage[app.appName] = app.usage.inSeconds;
      }

      for (var app in previousPeriodStats) {
        previousUsage[app.appName] = app.usage.inSeconds;
      }

      // Calculate incremental differences
      Map<String, int> incrementalUsage = {};

      for (String appName in currentUsage.keys) {
        int currentSeconds = currentUsage[appName] ?? 0;
        int previousSeconds = previousUsage[appName] ?? 0;
        int incrementalSeconds = currentSeconds - previousSeconds;

        // Log Chrome specifically for debugging
        if (appName.toLowerCase().contains('chrome')) {
          Logger.warning('CHROME INCREMENTAL: $appName - current=${currentSeconds}s, previous=${previousSeconds}s, incremental=${incrementalSeconds}s for hour $hourStart');
        }

        if (incrementalSeconds > 0) {
          incrementalUsage[appName] = incrementalSeconds;
        } else if (incrementalSeconds < 0) {
          // Negative incremental usage suggests data inconsistency - log warning but don't save
          Logger.warning('Negative incremental usage for $appName: current=${currentSeconds}s, previous=${previousSeconds}s, diff=${incrementalSeconds}s');
        }
      }

      Logger.info('Incremental calculation returned ${incrementalUsage.length} apps');
      return incrementalUsage;

    } catch (e) {
      Logger.error('Error calculating incremental usage: $e');

      // Fallback to direct method if incremental calculation fails
      Logger.info('Falling back to direct collection due to error');
      return await _getDirectUsageForHour(hourStart);
    }
  }

  /// Fallback method that uses direct hour-based collection.
  /// Used when incremental calculation fails.
  Future<Map<String, int>> _getDirectUsageForHour(DateTime hourStart) async {
    try {
      DateTime hourEnd = hourStart.add(const Duration(hours: 1));

      Logger.info('=== DIRECT COLLECTION DEBUG ===');
      Logger.info('Collecting direct usage for: $hourStart to $hourEnd');

      List<app_usage_package.AppUsageInfo> hourUsageStats =
          await _appUsage.getAppUsage(hourStart, hourEnd);

      Logger.info('app_usage package returned ${hourUsageStats.length} apps for direct collection');

      Map<String, int> directUsage = {};
      int totalSeconds = 0;

      for (var app in hourUsageStats) {
        if (app.usage.inSeconds > 0) {
          directUsage[app.appName] = app.usage.inSeconds;
          totalSeconds += app.usage.inSeconds;

          // Log Chrome and other significant apps
          if (app.appName.toLowerCase().contains('chrome') || app.usage.inSeconds > 300) {
            Logger.warning('DIRECT RAW DATA: ${app.appName} = ${app.usage.inSeconds}s (${(app.usage.inSeconds / 60).toStringAsFixed(1)}min)');
          }
        }
      }

      Logger.info('Direct collection total: ${totalSeconds}s (${(totalSeconds / 60).toStringAsFixed(1)}min) across ${directUsage.length} apps');

      return directUsage;

    } catch (e) {
      Logger.error('Error in direct usage collection: $e');
      return {};
    }
  }

  /// Validates the data we just saved to check for potential issues
  Future<void> _validateSavedData(DateTime hourStart) async {
    try {
      // Check what we actually saved in the database for this hour
      final hourEnd = hourStart.add(const Duration(hours: 1));

      // This is a simplified validation - in a real implementation you'd need access to the repository
      Logger.info('=== VALIDATION FOR HOUR $hourStart ===');
      Logger.info('Data saved for hour $hourStart to $hourEnd');

      // For now, just log that validation was attempted
      // In a full implementation, you'd query the database and compare totals

    } catch (e) {
      Logger.error('Error validating saved data: $e');
    }
  }

  /// Diagnostic method to understand app_usage package behavior
  /// This helps identify if the package returns cumulative or incremental data
  Future<void> diagnoseAppUsagePackageBehavior(DateTime hourStart) async {
    try {
      Logger.info('=== DIAGNOSTIC: APP_USAGE PACKAGE BEHAVIOR ===');

      DateTime hourEnd = hourStart.add(const Duration(hours: 1));
      DateTime halfHour = hourStart.add(const Duration(minutes: 30));

      // Test 1: Full hour
      Logger.info('Test 1: Full hour ($hourStart to $hourEnd)');
      List<app_usage_package.AppUsageInfo> fullHourData = await _appUsage.getAppUsage(hourStart, hourEnd);
      _logDiagnosticData('Full Hour', fullHourData);

      // Test 2: First half hour
      Logger.info('Test 2: First half ($hourStart to $halfHour)');
      List<app_usage_package.AppUsageInfo> firstHalfData = await _appUsage.getAppUsage(hourStart, halfHour);
      _logDiagnosticData('First Half', firstHalfData);

      // Test 3: Second half hour
      Logger.info('Test 3: Second half ($halfHour to $hourEnd)');
      List<app_usage_package.AppUsageInfo> secondHalfData = await _appUsage.getAppUsage(halfHour, hourEnd);
      _logDiagnosticData('Second Half', secondHalfData);

      // Test 4: Compare with larger time range
      DateTime twoHoursAgo = hourStart.subtract(const Duration(hours: 1));
      Logger.info('Test 4: Two hour range ($twoHoursAgo to $hourEnd)');
      List<app_usage_package.AppUsageInfo> twoHourData = await _appUsage.getAppUsage(twoHoursAgo, hourEnd);
      _logDiagnosticData('Two Hours', twoHourData);

      Logger.info('=== DIAGNOSTIC COMPLETE ===');

    } catch (e) {
      Logger.error('Error in diagnostic: $e');
    }
  }

  void _logDiagnosticData(String testName, List<app_usage_package.AppUsageInfo> data) {
    Logger.info('$testName: ${data.length} apps');

    for (var app in data) {
      if (app.appName.toLowerCase().contains('chrome') && app.usage.inSeconds > 0) {
        Logger.warning('$testName - CHROME: ${app.appName} = ${app.usage.inSeconds}s (${(app.usage.inSeconds / 60).toStringAsFixed(1)}min)');
      }
    }

    int totalSeconds = data.fold(0, (sum, app) => sum + app.usage.inSeconds);
    Logger.info('$testName - Total: ${totalSeconds}s (${(totalSeconds / 60).toStringAsFixed(1)}min)');
  }

  @override
  Future<void> stopTracking() async {
    try {
      await workManagerChannel.invokeMethod('stopPeriodicAppUsageWork');
      Logger.info('WorkManager app usage tracking stopped');
    } catch (e) {
      Logger.error('Failed to stop WorkManager tracking: $e');
    }

    periodicTimer?.cancel();
    periodicTimer = null; // Clear any remaining timer instance.
    Logger.info('Android app usage tracking stopped');
  }

  /// Checks if the app has permission to access usage statistics
  /// Returns true if permission is granted, false otherwise
  @override
  Future<bool> checkUsageStatsPermission() async {
    try {
      // Check usage statistics permission from Kotlin side
      final hasPermission = await appUsageStatsChannel.invokeMethod<bool>('checkUsageStatsPermission');
      return hasPermission ?? false;
    } catch (e) {
      Logger.error('Error checking usage stats permission: $e');

      // Use backup check in case of method channel error
      try {
        // Try to fetch data for a small time range as backup
        await _appUsage.getAppUsage(
          DateTime.now().subtract(const Duration(minutes: 5)),
          DateTime.now(),
        );
        return true;
      } catch (backupError) {
        Logger.error('Backup permission check failed: $backupError');
        return false;
      }
    }
  }

  /// Opens the settings page to request usage statistics permission
  /// This should be called when user interaction is appropriate (e.g., after a user clicks a button)
  @override
  Future<void> requestUsageStatsPermission() async {
    try {
      // Open usage access settings page
      await appUsageStatsChannel.invokeMethod('openUsageAccessSettings');
    } catch (e) {
      Logger.error('Error requesting usage stats permission: $e');
    }
  }



  /// Gets the last collection timestamp from settings
  /// Returns null if no previous collection timestamp exists
  Future<DateTime?> _getLastCollectionTimestamp() async {
    try {
      final setting = await _settingRepository.getByKey(SettingKeys.appUsageLastCollectionTimestamp);
      if (setting != null) {
        final timestamp = int.tryParse(setting.value);
        if (timestamp != null) {
          return DateTime.fromMillisecondsSinceEpoch(timestamp, isUtc: true);
        }
      }
    } catch (e) {
      Logger.error('Error getting last collection timestamp: $e');
    }
    return null;
  }

  /// Saves the last collection timestamp to settings
  Future<void> _saveLastCollectionTimestamp(DateTime timestamp) async {
    try {
      final timestampValue = timestamp.toUtc().millisecondsSinceEpoch.toString();

      final existingSetting = await _settingRepository.getByKey(SettingKeys.appUsageLastCollectionTimestamp);
      if (existingSetting != null) {
        existingSetting.value = timestampValue;
        existingSetting.modifiedDate = DateTime.now().toUtc();
        await _settingRepository.update(existingSetting);
      } else {
        final newSetting = Setting(
          id: app_key_helper.KeyHelper.generateStringId(),
          key: SettingKeys.appUsageLastCollectionTimestamp,
          value: timestampValue,
          valueType: SettingValueType.string,
          createdDate: DateTime.now().toUtc(),
        );
        await _settingRepository.add(newSetting);
      }
    } catch (e) {
      Logger.error('Error saving last collection timestamp: $e');
    }
  }
}
