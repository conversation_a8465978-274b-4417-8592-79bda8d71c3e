# Debugging 2x App Usage Discrepancy

## Problem Statement
After implementing the incremental data collection fix, we still observe a 2x over-reporting issue:
- **Digital Wellbeing**: Chrome 39 minutes
- **Our App**: Chrome 79 minutes (2.03x higher)

## Potential Root Causes

### 1. **First Hour Collection Issue**
The first hour uses direct collection (`_getDirectUsageForHour`) instead of incremental calculation. This might be returning cumulative data from device boot rather than just the target hour.

### 2. **app_usage Package Behavior**
The `app_usage` package might be interpreting Android's `UsageStatsManager` differently than Digital Wellbeing, potentially:
- Including background/system usage that Digital Wellbeing excludes
- Using different time granularity or aggregation methods
- Counting different types of app interactions

### 3. **Time Range Interpretation**
Our time ranges might overlap or include periods that Digital Wellbeing doesn't count.

### 4. **Double Collection**
We might be accidentally collecting the same time period multiple times.

## Debugging Strategy

### Phase 1: Log Analysis
Look for these key log entries to understand the data flow:

#### **Collection Session Logs:**
```
=== PROCESSING HOUR 1/1: [timestamp] ===
=== COLLECTING HOUR: [timestamp] ===
```

#### **Incremental vs Direct Collection:**
```
=== INCREMENTAL CALCULATION DEBUG ===
Is first collection: true/false
Using direct collection for first hour
Using incremental calculation
```

#### **Raw Data from app_usage Package:**
```
=== DIRECT COLLECTION DEBUG ===
app_usage package returned X apps for direct collection
DIRECT RAW DATA: Chrome = Xs (X.Xmin)
Direct collection total: Xs (X.Xmin) across X apps
```

#### **Chrome-Specific Tracking:**
```
CHROME DIRECT: [app_name] = Xs (X.Xmin) for first hour [timestamp]
CHROME INCREMENTAL: [app_name] - current=Xs, previous=Xs, incremental=Xs
SAVING CHROME: [app_name] = Xs (X.Xmin) for hour [timestamp]
```

#### **Diagnostic Package Behavior:**
```
=== DIAGNOSTIC: APP_USAGE PACKAGE BEHAVIOR ===
Full Hour - CHROME: [app_name] = Xs (X.Xmin)
First Half - CHROME: [app_name] = Xs (X.Xmin)
Second Half - CHROME: [app_name] = Xs (X.Xmin)
Two Hours - CHROME: [app_name] = Xs (X.Xmin)
```

### Phase 2: Data Analysis Questions

#### **Question 1: Is the raw data correct?**
- What does `app_usage.getAppUsage(hourStart, hourEnd)` return for Chrome?
- Does this match what Digital Wellbeing shows for the same period?

#### **Question 2: Are we collecting multiple times?**
- Check if the same hour appears multiple times in processing logs
- Verify `overwrite: true` is working correctly

#### **Question 3: Is the time range correct?**
- Compare our hour boundaries with Digital Wellbeing's time periods
- Check if we're including partial hours or overlapping periods

#### **Question 4: Package behavior differences?**
- Do the diagnostic tests show cumulative behavior?
- Is `First Half + Second Half = Full Hour`?
- Is `Two Hours > Full Hour` (indicating cumulative data)?

### Phase 3: Expected Findings

#### **If the issue is in first hour collection:**
- Direct collection returns ~79 minutes for Chrome
- Diagnostic shows cumulative behavior (Two Hours > Full Hour)
- Need to implement proper first-hour incremental calculation

#### **If the issue is in package interpretation:**
- Raw data from package shows ~79 minutes
- Digital Wellbeing uses different criteria/filters
- Need to understand what Digital Wellbeing excludes

#### **If the issue is double collection:**
- Same hour appears multiple times in logs
- Total saved > raw data from package
- Need to fix collection logic

## Next Steps Based on Findings

### **Scenario A: Package Returns Cumulative Data**
Implement proper incremental calculation even for first hour by:
1. Using a baseline time (device boot or app install)
2. Always calculating differences between time periods
3. Never using direct hour-based collection

### **Scenario B: Package Includes Extra Data**
Filter or adjust the data to match Digital Wellbeing by:
1. Excluding certain types of usage (background, system, etc.)
2. Applying time-based filters
3. Using different aggregation methods

### **Scenario C: Collection Logic Error**
Fix the collection process by:
1. Ensuring no duplicate hour processing
2. Verifying overwrite behavior
3. Checking time zone handling

## Testing Instructions

1. **Deploy the debug version** with enhanced logging
2. **Restart the device** to ensure clean first-hour collection
3. **Use Chrome for exactly 30 minutes** during the first hour
4. **Check Digital Wellbeing** after the hour completes
5. **Analyze logs** using the patterns above
6. **Compare results** and identify the discrepancy source

## Log Search Patterns

```bash
# Find Chrome-related entries
grep -i "chrome" app_logs.txt

# Find collection sessions
grep "=== COLLECTING HOUR" app_logs.txt

# Find diagnostic data
grep "DIAGNOSTIC" app_logs.txt

# Find raw package data
grep "DIRECT RAW DATA" app_logs.txt
```
