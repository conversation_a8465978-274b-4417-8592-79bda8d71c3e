# App Usage Data Inconsistency Fix

## Problem Summary

The app was reporting significantly inflated usage statistics compared to Android's Digital Wellbeing:
- **Digital Wellbeing**: Firefox 2h 5m, Total screen time 4h 21m  
- **Our App**: Firefox 46h 2m (22x higher!)

## Root Cause Analysis

### Primary Issue: Cumulative Data Collection
The `app_usage` package (v4.0.1) calls Android's `UsageStatsManager.queryUsageStats()`, which returns **cumulative usage data** for the specified time period, not incremental hourly data.

**The Problem:**
1. Hour 1 (10:00-11:00): `getAppUsage()` returns 30 minutes of Firefox usage
2. Hour 2 (11:00-12:00): `getAppUsage()` returns 45 minutes of Firefox usage (30 previous + 15 new)
3. Our app stored: 30 + 45 = 75 minutes, but actual usage was only 45 minutes

### Secondary Issues:
- **Time Zone Handling**: Inconsistent local/UTC time handling
- **Collection Overlaps**: Potential overlapping time periods
- **Package Behavior**: Different interpretation than Digital Wellbeing

## Solution Implemented

### 1. Incremental Data Collection
Replaced direct hour-based collection with incremental calculation:

```dart
// OLD METHOD (Problematic)
hourUsageStats = await _appUsage.getAppUsage(hourStart, hourEnd);
// This returns cumulative data, causing double-counting

// NEW METHOD (Fixed)
currentPeriodStats = await _appUsage.getAppUsage(previousHourStart, hourEnd);
previousPeriodStats = await _appUsage.getAppUsage(previousHourStart, hourStart);
incrementalUsage = currentPeriodStats - previousPeriodStats;
```

### 2. Enhanced Logging and Debugging
Added comprehensive logging to track:
- Raw data from app_usage package
- Incremental calculations
- Time zone conversions
- Firefox-specific tracking for validation

### 3. Improved Time Zone Handling
- Consistent local time for collection
- Proper UTC conversion for storage
- Clear logging of time zone states

### 4. Fallback Mechanisms
- Direct collection for first hour (no previous data)
- Fallback to direct method if incremental calculation fails
- Error handling for edge cases

## Key Changes Made

### File: `android_app_usage_service.dart`

1. **`_collectUsageForSingleHour()`**: Now uses incremental approach
2. **`_getIncrementalUsageForHour()`**: New method for calculating incremental usage
3. **`_getDirectUsageForHour()`**: Fallback method for edge cases
4. **Enhanced logging**: Detailed tracking of data collection process

### File: `base_app_usage_service.dart`

1. **`saveTimeRecord()`**: Added detailed logging for data storage

## Expected Results

After this fix, the app should:
1. **Accurate Data**: Match or closely align with Digital Wellbeing statistics
2. **No Double-Counting**: Eliminate cumulative data issues
3. **Better Debugging**: Comprehensive logs for troubleshooting
4. **Robust Collection**: Handle edge cases and errors gracefully

## Testing and Validation

### To Test the Fix:
1. **Clear existing data** (optional): Reset app usage data to start fresh
2. **Monitor logs**: Check for "INCREMENTAL" and "FIREFOX" log entries
3. **Compare with Digital Wellbeing**: Verify usage times align
4. **Check for negative values**: Should not see negative incremental usage

### Log Entries to Watch:
- `=== COLLECTING USAGE DATA (INCREMENTAL METHOD) ===`
- `FIREFOX INCREMENTAL: [app] - [time]s for hour [hour]`
- `FIREFOX CALCULATION: current=[x]s, previous=[y]s, incremental=[z]s`
- `Calculated incremental usage for [n] apps`

## Monitoring

The fix includes extensive logging to help monitor data collection:
- Collection session summaries
- Per-app incremental calculations
- Error handling and fallbacks
- Time zone conversion tracking

## Future Improvements

1. **Data Validation**: Compare totals with system statistics
2. **Collection Optimization**: Reduce API calls if possible
3. **User Feedback**: Allow users to report discrepancies
4. **Alternative Packages**: Evaluate other app usage tracking libraries

## Notes

- The fix maintains backward compatibility
- Existing data is not affected (new collection method only)
- Fallback mechanisms ensure continued operation even if incremental calculation fails
- Enhanced logging can be reduced in production if needed
