# App Usage Refresh Button Feature

## Overview

Added a manual refresh button to the app usage statistics page that allows users to immediately trigger data collection and refresh the usage statistics list without waiting for the next scheduled collection.

## Features

### 🔄 **Manual Data Collection**
- Triggers `IAppUsageService.startTracking()` to collect latest usage data from Android's UsageStatsManager
- Bypasses the normal hourly collection schedule
- Useful for testing the incremental data collection fixes
- Allows users to see real-time updates of their usage patterns

### 🎯 **Enhanced User Experience**
- **Loading Indicator**: Shows a circular progress indicator while refreshing
- **Button State Management**: Disables the button during refresh to prevent multiple simultaneous requests
- **Success Feedback**: Shows green snackbar when refresh completes successfully
- **Error Handling**: Shows red snackbar with error details if refresh fails

### 📱 **UI Integration**
- Located in the app bar next to the settings button
- Uses the existing refresh icon (Icons.refresh)
- Maintains consistent styling with other app bar buttons
- Includes tooltip for accessibility

## Implementation Details

### **State Management**
```dart
bool _isRefreshing = false; // Tracks refresh state
```

### **Refresh Method**
```dart
Future<void> _onRefresh() async {
  if (_isRefreshing) return; // Prevent multiple simultaneous refreshes
  
  setState(() => _isRefreshing = true);
  
  try {
    // Trigger data collection
    await _deviceAppUsageService.startTracking();
    
    // Allow time for collection to complete
    await Future.delayed(const Duration(seconds: 1));
    
    // Refresh UI
    _appUsagesService.notifyRefresh();
    
    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(/* success snackbar */);
  } catch (e) {
    // Show error message
    ScaffoldMessenger.of(context).showSnackBar(/* error snackbar */);
  } finally {
    setState(() => _isRefreshing = false);
  }
}
```

### **UI Button**
```dart
IconButton(
  icon: _isRefreshing 
      ? CircularProgressIndicator(/* loading indicator */)
      : Icon(Icons.refresh),
  onPressed: _isRefreshing ? null : _onRefresh,
  // ... styling
)
```

## Usage Scenarios

### **For Testing**
1. **After making changes** to the incremental data collection logic
2. **When verifying fixes** for the 2x over-reporting issue
3. **During development** to see immediate results without waiting for scheduled collection

### **For Users**
1. **Real-time monitoring**: Check usage immediately after using an app
2. **Data verification**: Compare with Android's Digital Wellbeing
3. **Troubleshooting**: Force refresh if data seems outdated

## User Flow

1. **User opens** App Usage Statistics page
2. **User taps** the refresh button in the app bar
3. **Button shows** loading indicator and becomes disabled
4. **System triggers** data collection from Android
5. **UI refreshes** with latest data
6. **User sees** success/error feedback
7. **Button returns** to normal state

## Error Handling

### **Graceful Degradation**
- If refresh fails, the existing data remains visible
- Error message provides context about what went wrong
- Button returns to normal state for retry

### **Common Error Scenarios**
- **Permission issues**: App usage permission not granted
- **System errors**: Android UsageStatsManager unavailable
- **Network issues**: If any network-dependent operations fail

## Benefits

### **For Development**
- ✅ **Immediate feedback** when testing fixes
- ✅ **No waiting** for scheduled collection cycles
- ✅ **Easy verification** of data accuracy
- ✅ **Better debugging** experience

### **For Users**
- ✅ **Real-time data** access
- ✅ **Control over updates** 
- ✅ **Better user experience**
- ✅ **Transparency** in data collection

## Technical Notes

### **Performance Considerations**
- Refresh is rate-limited by the button state to prevent spam
- 1-second delay ensures data collection has time to complete
- UI updates are batched to minimize redraws

### **Integration with Existing System**
- Works alongside the existing WorkManager scheduled collection
- Uses the same data collection service (`IAppUsageService`)
- Leverages existing UI refresh mechanisms (`AppUsagesService.notifyRefresh()`)

### **Future Enhancements**
- Could add pull-to-refresh gesture on the list
- Could show more detailed progress information
- Could add automatic refresh intervals as a user setting

## Testing

### **Manual Testing Steps**
1. Open App Usage Statistics page
2. Note current usage data
3. Use an app (e.g., Chrome) for a few minutes
4. Tap the refresh button
5. Verify the loading indicator appears
6. Verify the data updates with new usage
7. Verify success message appears

### **Edge Cases to Test**
- Refresh while no permission granted
- Refresh with no usage data available
- Multiple rapid refresh attempts
- Refresh during app backgrounding/foregrounding
