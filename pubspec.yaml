name: whph
description: A comprehensive productivity app designed to help you manage tasks, develop new habits, and optimize your time.
publish_to: "none"
version: 0.9.7+44

environment:
  sdk: ^3.5.3
  flutter: "3.32.0"

scripts:
  # This section defines custom commands you can run using the rps package.
  # To install rps globally: `dart pub global activate rps`
  # To run commands: `rps <command>`
  # For example, to run the `clean` command: `rps clean`
  clean: bash scripts/clean.sh
  format: dart format . -l 120
  security-check: bash scripts/security_validation.sh
  gen: flutter pub run build_runner clean && flutter pub run build_runner build --delete-conflicting-outputs && rps format
  gen:icons: flutter pub run icons_launcher:create
  gen:migrate: flutter pub run drift_dev make-migrations && rps format
  gen:changelog: bash scripts/create_changelog.sh --auto
  gen:changelog:all: bash scripts/create_changelog.sh --all-versions --auto
  build:apk: flutter build apk --release --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons
  build:apk:reproducible: flutter clean && flutter pub get && bash scripts/security_validation.sh && flutter build apk --release --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons
  build:bundle: flutter build appbundle --release --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons
  build:bundle:reproducible: flutter clean && flutter pub get && bash scripts/security_validation.sh && flutter build appbundle --release --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons
  release:android: bash scripts/security_validation.sh && flutter build apk --release --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons
  release:android:bundle: bash scripts/security_validation.sh && flutter build appbundle --release --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons
  release:android:reproducible: flutter clean && flutter pub get && bash scripts/security_validation.sh && flutter build apk --release --split-debug-info=build/app/outputs/symbols --obfuscate --tree-shake-icons
  release:linux: flutter build linux --release
  release:windows: flutter build windows --release
  release:windows:setup: flutter build windows --release && "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" windows\setup-wizard\installer.iss
  test: flutter test
  test:ci: act -j build
  test:ci:android: act -W .github/workflows/flutter-ci.android.yml -j build --secret-file .secrets
  test:ci:fdroid: bash scripts/test_ci_fdroid.sh
  test:ci:linux: act -W .github/workflows/flutter-ci.linux.yml -j build --secret-file .secrets
  test:ci:windows: act -W .github/workflows/flutter-ci.windows.yml -j build --secret-file .secrets
  run:demo: flutter run --dart-define=DEMO_MODE=true
  version:major: bash scripts/version_bump.sh major
  version:minor: bash scripts/version_bump.sh minor
  version:patch: bash scripts/version_bump.sh patch
  version:push: cd android/fdroid && git push && cd ../.. && git push && git push --tags

dependencies:
  flutter:
    sdk: flutter
  test: ^1.25.8

  # Core Packages
  acore:
    path: lib/corePackages/acore
  # Data
  drift: ^2.21.0
  nanoid2: ^2.0.1
  sqflite: ^2.4.0
  sqlite3_flutter_libs: ^0.5.26
  # Utils
  archive: ^4.0.5
  audioplayers: ^6.1.0
  csv: ^6.0.0
  dart_json_mapper: ^2.2.13
  device_info_plus: ^11.4.0
  file_picker: ^10.0.0
  file_saver: ^0.2.12
  flutter_local_notifications: ^19.0.0
  http: ^1.3.0
  intl: ^0.20.2
  kiwi: ^5.0.1
  mediatr: ^0.2.1
  meta: ^1.15.0
  network_info_plus: ^6.1.3
  path: ^1.8.3
  path_provider: ^2.1.1
  timezone: ^0.10.0
  tray_manager: ^0.5.0
  url_launcher: ^6.3.1
  wakelock_plus: ^1.2.8
  web_socket_channel: ^3.0.1
  window_manager: ^0.4.3
  equatable: ^2.0.7
  # UI
  calendar_date_picker2: ^2.0.0
  cupertino_icons: ^1.0.8
  easy_localization: ^3.0.7
  easy_localization_multi: ^0.2.0
  easy_localization_yaml: ^0.1.2
  fl_chart: ^1.0.0
  flutter_colorpicker: ^1.1.0
  flutter_confetti: ^0.5.1
  markdown_editor_plus: ^0.2.15
  # Desktop
  launch_at_startup: ^0.5.1
  qr_flutter: ^4.1.0
  # Mobile
  qr_code_scanner_plus: ^2.0.6
  # Android
  android_intent_plus: ^5.3.0
  app_usage: ^4.0.1
  permission_handler: ^12.0.0+1
  modal_bottom_sheet: ^3.0.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  rps: ^0.7.0
  drift_dev: ^2.21.2
  icons_launcher: ^3.0.0
  yaml: ^3.1.3

flutter:
  uses-material-design: true
  assets:
    - lib/src/core/domain/shared/assets/images/
    - lib/src/presentation/ui/shared/assets/sounds/
    - lib/src/presentation/ui/shared/assets/locales/
    - lib/src/presentation/ui/features/about/assets/locales/
    - lib/src/presentation/ui/features/app_usages/assets/locales/
    - lib/src/presentation/ui/features/calendar/assets/locales/
    - lib/src/presentation/ui/features/habits/assets/locales/
    - lib/src/presentation/ui/features/settings/assets/locales/
    - lib/src/presentation/ui/features/tags/assets/locales/
    - lib/src/presentation/ui/features/sync/assets/locales/
    - lib/src/presentation/ui/features/tasks/assets/locales/
    - lib/src/presentation/ui/features/tasks/assets/sounds/
    - lib/src/presentation/ui/features/notes/assets/locales/
